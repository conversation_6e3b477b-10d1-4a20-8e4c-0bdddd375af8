import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class FirestoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // 사용자 정보 저장
  static Future<void> saveUserInfo({
    required String uid,
    required String email,
    String? displayName,
    String? photoURL,
    required String signInMethod,
  }) async {
    try {
      final userDoc = _firestore.collection('users').doc(uid);

      final userData = {
        'uid': uid,
        'email': email,
        'displayName': displayName,
        'photoURL': photoURL,
        'signInMethod': signInMethod,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'isActive': true,
      };

      // 사용자 정보가 이미 존재하는지 확인
      final docSnapshot = await userDoc.get();

      if (docSnapshot.exists) {
        // 기존 사용자 정보 업데이트
        await userDoc.update({
          'displayName': displayName,
          'photoURL': photoURL,
          'updatedAt': FieldValue.serverTimestamp(),
          'lastSignInAt': FieldValue.serverTimestamp(),
        });
      } else {
        // 새 사용자 정보 저장
        await userDoc.set(userData);
      }

      debugPrint('사용자 정보 저장 완료: $uid');
    } catch (e) {
      debugPrint('사용자 정보 저장 오류: $e');
      throw '사용자 정보 저장 중 오류가 발생했습니다.';
    }
  }

  // 로그인 히스토리 저장
  static Future<void> saveLoginHistory({
    required String uid,
    required String signInMethod,
    String? deviceInfo,
  }) async {
    try {
      await _firestore.collection('login_history').add({
        'uid': uid,
        'signInMethod': signInMethod,
        'deviceInfo': deviceInfo ?? 'Unknown Device',
        'timestamp': FieldValue.serverTimestamp(),
        'ipAddress': 'Unknown', // 실제 구현시 IP 주소 수집 가능
      });

      debugPrint('로그인 히스토리 저장 완료: $uid');
    } catch (e) {
      debugPrint('로그인 히스토리 저장 오류: $e');
      // 로그인 히스토리 저장 실패는 로그인 자체를 실패시키지 않음
    }
  }

  // 사용자 정보 조회
  static Future<Map<String, dynamic>?> getUserInfo(String uid) async {
    try {
      final docSnapshot = await _firestore.collection('users').doc(uid).get();

      if (docSnapshot.exists) {
        return docSnapshot.data();
      }
      return null;
    } catch (e) {
      debugPrint('사용자 정보 조회 오류: $e');
      return null;
    }
  }

  // 사용자 정보 스트림
  static Stream<DocumentSnapshot<Map<String, dynamic>>> getUserInfoStream(
    String uid,
  ) {
    return _firestore.collection('users').doc(uid).snapshots();
  }

  // 사용자 프로필 업데이트
  static Future<void> updateUserProfile({
    required String uid,
    String? displayName,
    String? photoURL,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (displayName != null) {
        updateData['displayName'] = displayName;
      }

      if (photoURL != null) {
        updateData['photoURL'] = photoURL;
      }

      await _firestore.collection('users').doc(uid).update(updateData);

      debugPrint('사용자 프로필 업데이트 완료: $uid');
    } catch (e) {
      debugPrint('사용자 프로필 업데이트 오류: $e');
      throw '프로필 업데이트 중 오류가 발생했습니다.';
    }
  }

  // 사용자 계정 비활성화
  static Future<void> deactivateUser(String uid) async {
    try {
      await _firestore.collection('users').doc(uid).update({
        'isActive': false,
        'deactivatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('사용자 계정 비활성화 완료: $uid');
    } catch (e) {
      debugPrint('사용자 계정 비활성화 오류: $e');
      throw '계정 비활성화 중 오류가 발생했습니다.';
    }
  }

  // 로그인 히스토리 조회
  static Future<List<Map<String, dynamic>>> getLoginHistory(
    String uid, {
    int limit = 10,
  }) async {
    try {
      final querySnapshot = await _firestore
          .collection('login_history')
          .where('uid', isEqualTo: uid)
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
    } catch (e) {
      debugPrint('로그인 히스토리 조회 오류: $e');
      return [];
    }
  }
}
