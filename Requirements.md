# 🚀 Flutter Boilerplate (Vibe Coding Edition)

미래의 Flutter 앱 개발을 더 빠르고, 체계적으로!  
자주 반복되는 구조와 필수 기능을 미리 세팅한 바이브코딩 표준 보일러플레이트입니다.

---

## ✨ 포함 기능

- **Splash Screen**  
  앱 시작 시 브랜드 로고 & 초기 데이터 로딩 처리

- **인증 시스템 (Firebase / Clerk.com)**

  - 이메일/비밀번호 회원가입 & 로그인
  - (옵션) 구글/애플 소셜 로그인
  - 자동 로그인, 토큰 관리, 비밀번호 찾기
  - 인증 서비스는 Firebase, Clerk.com 중 선택 사용 가능

- **Neon.tech 기반 백엔드 연동**

  - Neon Postgres 연동 예제
  - REST API or GraphQL 구조 샘플
  - 공통 API 에러 처리 및 응답 파싱 구조

- **OpenRouter 기반 AI 연동**

  - ChatGPT, Claude 등 LLM API 호출 구조 샘플
  - 프롬프트 템플릿, API 키/쿼터 예외 처리 예시

- **앱 설정 화면**

  - 다크/라이트 테마 전환
  - 언어 설정 (한/영 다국어)
  - 푸시 알림 ON/OFF, 로그아웃, 버전정보 등

- **푸시 알림**

  - FCM 기본 연동 및 토큰 관리
  - 백그라운드/포그라운드 알림 처리
  - 알림 클릭 → 특정 화면 진입(딥링크) 샘플

- **글로벌 예외 처리**
  - API/네트워크/인증 에러 핸들러
  - 공통 다이얼로그/로딩/입력/버튼 위젯

---

## 🏗️ 프로젝트 구조 예시

lib/
├── core/ # 공통 유틸/서비스/상수
├── features/ # 기능별 폴더(인증, 설정, AI 등)
├── shared/ # 공용 컴포넌트/위젯
├── l10n/ # 다국어 리소스
└── main.dart # 진입점

---

## ⚙️ 기술 스택

- Flutter (최신 안정화)
- 상태관리: Riverpod / Bloc / Provider (선택)
- 인증: Firebase Auth or Clerk.com
- API 통신: Dio or http
- DB: Neon.tech (Postgres)
- 푸시: FCM (Firebase Cloud Messaging)
- AI 연동: OpenRouter API
- 로컬 저장: Hive or Drift (옵션)
- 라우팅: go_router or auto_route
- 다국어: flutter_localizations

---

## 🚩 개발/확장 가이드

- .env로 환경변수 관리(개발/운영 분리)
- 모든 인증/네트워크 에러는 공통 핸들러로 관리
- 각 기능은 feature 폴더별로 독립적으로 확장/재사용 가능
- 추가 기능이 필요하다면 feature 단위로 분리해서 개발

---

## 🧪 테스트/품질 관리

- 주요 기능 단위 테스트 샘플 포함
- 코드 컨벤션(Effective Dart) 및 null safety 적용
- UI/UX는 기본 반응형 구조로 설계

---

## 📝 추가 참고 사항

- Sentry/Firebase Crashlytics 연동(옵션)
- Firebase Analytics 연동(옵션)
- CI/CD(GitHub Actions 등) 예제 파일 제공(옵션)
- 자세한 기능별 사용법/설정법은 각 feature 폴더 내 README 참고
