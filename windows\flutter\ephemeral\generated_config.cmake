# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\DevTools\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\DevTools\\FlutterApp\\flutter_pro_test" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\DevTools\\flutter"
  "PROJECT_DIR=C:\\DevTools\\FlutterApp\\flutter_pro_test"
  "FLUTTER_ROOT=C:\\DevTools\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\DevTools\\FlutterApp\\flutter_pro_test\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\DevTools\\FlutterApp\\flutter_pro_test"
  "FLUTTER_TARGET=C:\\DevTools\\FlutterApp\\flutter_pro_test\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049MDc3YjRhNGNlMQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049NzJmMmIxOGJiMA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\DevTools\\FlutterApp\\flutter_pro_test\\.dart_tool\\package_config.json"
)
