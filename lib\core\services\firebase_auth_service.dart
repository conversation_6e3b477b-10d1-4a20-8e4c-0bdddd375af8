import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart';
import 'firestore_service.dart';

class FirebaseAuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn();

  // 현재 사용자 스트림
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // 현재 사용자
  static User? get currentUser => _auth.currentUser;

  // 이메일/비밀번호 회원가입
  static Future<UserCredential?> signUpWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Firestore에 사용자 정보 저장
      if (result.user != null) {
        await FirestoreService.saveUserInfo(
          uid: result.user!.uid,
          email: email,
          displayName: result.user!.displayName,
          photoURL: result.user!.photoURL,
          signInMethod: 'email',
        );

        // 로그인 히스토리 저장
        await FirestoreService.saveLoginHistory(
          uid: result.user!.uid,
          signInMethod: 'email_signup',
        );
      }

      return result;
    } on FirebaseAuthException catch (e) {
      debugPrint('회원가입 오류: ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('회원가입 오류: $e');
      throw '회원가입 중 오류가 발생했습니다.';
    }
  }

  // 이메일/비밀번호 로그인
  static Future<UserCredential?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // 로그인 히스토리 저장
      if (result.user != null) {
        await FirestoreService.saveLoginHistory(
          uid: result.user!.uid,
          signInMethod: 'email',
        );

        // 사용자 정보 업데이트 (마지막 로그인 시간)
        await FirestoreService.saveUserInfo(
          uid: result.user!.uid,
          email: email,
          displayName: result.user!.displayName,
          photoURL: result.user!.photoURL,
          signInMethod: 'email',
        );
      }

      return result;
    } on FirebaseAuthException catch (e) {
      debugPrint('로그인 오류: ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('로그인 오류: $e');
      throw '로그인 중 오류가 발생했습니다.';
    }
  }

  // 구글 로그인
  static Future<UserCredential?> signInWithGoogle() async {
    try {
      // 구글 로그인 트리거
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // 사용자가 로그인을 취소한 경우
        return null;
      }

      // 구글 인증 정보 획득
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Firebase 인증 자격 증명 생성
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Firebase로 로그인
      final UserCredential result = await _auth.signInWithCredential(
        credential,
      );

      // Firestore에 사용자 정보 저장/업데이트
      if (result.user != null) {
        await FirestoreService.saveUserInfo(
          uid: result.user!.uid,
          email: result.user!.email ?? '',
          displayName: result.user!.displayName,
          photoURL: result.user!.photoURL,
          signInMethod: 'google',
        );

        // 로그인 히스토리 저장
        await FirestoreService.saveLoginHistory(
          uid: result.user!.uid,
          signInMethod: 'google',
        );
      }

      return result;
    } on FirebaseAuthException catch (e) {
      debugPrint('구글 로그인 오류: ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('구글 로그인 오류: $e');
      throw '구글 로그인 중 오류가 발생했습니다.';
    }
  }

  // 로그아웃
  static Future<void> signOut() async {
    try {
      await Future.wait([_auth.signOut(), _googleSignIn.signOut()]);
    } catch (e) {
      debugPrint('로그아웃 오류: $e');
      throw '로그아웃 중 오류가 발생했습니다.';
    }
  }

  // 비밀번호 재설정
  static Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      debugPrint('비밀번호 재설정 오류: ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('비밀번호 재설정 오류: $e');
      throw '비밀번호 재설정 중 오류가 발생했습니다.';
    }
  }

  // Firebase 인증 예외 처리
  static String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return '비밀번호가 너무 약합니다.';
      case 'email-already-in-use':
        return '이미 사용 중인 이메일입니다.';
      case 'user-not-found':
        return '사용자를 찾을 수 없습니다.';
      case 'wrong-password':
        return '잘못된 비밀번호입니다.';
      case 'invalid-email':
        return '유효하지 않은 이메일 형식입니다.';
      case 'user-disabled':
        return '비활성화된 계정입니다.';
      case 'too-many-requests':
        return '너무 많은 요청이 발생했습니다. 잠시 후 다시 시도해주세요.';
      case 'operation-not-allowed':
        return '허용되지 않은 작업입니다.';
      default:
        return e.message ?? '인증 오류가 발생했습니다.';
    }
  }
}
